<script setup lang="ts">
import fishbushDead from "@/public/fishbush_dead.gif";
import { useI18n } from "vue-i18n";
import { ref } from "vue";

const { t } = useI18n();

const props = defineProps({
	code: {
		type: Number,
		required: true,
	}
});

// Reactivity test: counter with +1 button
const counter = ref(0);

const incrementCounter = () => {
	counter.value++;
};
</script>

<template>
  <div class="content">
    <div class="content-container text-center my-8">
      <h6>[{{props.code}}] {{ t('error_500_msg')}}</h6>

      <!-- Reactivity test: counter with +1 button -->
      <div class="reactivity-test mt-6 p-4 border border-gray-300 rounded-lg bg-gray-50">
        <h3 class="text-lg font-semibold mb-3">Reactivity Test</h3>
        <div class="flex items-center justify-center gap-4">
          <span class="text-xl font-mono">Counter: {{ counter }}</span>
          <button
            @click="incrementCounter"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            +1
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="w-full h-full text-center flex items-center justify-center">
    <img :src="fishbushDead" alt="Upsík, něco se nepovedlo" class="fishbush-down-img">
  </div>
</template>

<style scoped>
.fishbush-down-img {
  @apply object-contain m-auto p-4 w-8/12 md:w-6/12 lg:w-4/12 xl:w-3/12;
}
</style>