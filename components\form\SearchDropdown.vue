<!-- SchoolDropdown.vue -->
<script setup lang="ts">
import { Searcher } from "fast-fuzzy";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps<{
	modelValue: SchoolEntity | null;
	schools: Array<SchoolEntity>;
	error?: string;
}>();

let searcher = new Searcher(props.schools, {
	threshold: 0.7,
	keySelector: (school) => school.attributes?.name ?? "",
});

const emit = defineEmits<{
	(e: "update:modelValue", value: SchoolEntity | null): void;
	(e: "blur"): void;
}>();

const inputValue = ref(props.modelValue?.attributes?.name || "");

const dropdownVisible = ref(false);

const filteredSchools = computed(() => {
	if (inputValue.value === "") {
		return props.schools;
	}
	return searcher.search(inputValue.value);
});

watch(
	() => props.modelValue,
	(newValue) => {
		inputValue.value = newValue?.attributes?.name || "";
	},
);

watch(
	() => props.schools,
	(newSchools) => {
		searcher = new Searcher(newSchools, {
			threshold: 0.7,
			keySelector: (school) => school.attributes?.name ?? "",
		});
	},
);

const selectSchool = (selectedSchool: SchoolEntity) => {
	inputValue.value = selectedSchool.attributes?.name || "";
	dropdownVisible.value = false;
	emit("update:modelValue", selectedSchool);
	emit("blur");
};

const handleChange = () => {
	dropdownVisible.value = true;
};

const handleBlur = () => {
	setTimeout(() => {
		dropdownVisible.value = false;
		const matchingSchool = props.schools.find(
			(s) => s.attributes?.name === inputValue.value,
		);
		emit("update:modelValue", matchingSchool || null);
		emit("blur");
	}, 200);
};

const handleClickOutside = (event: MouseEvent) => {
	const target = event.target as HTMLElement;
	const inputElement = document.getElementById("school");
	const dropdownElement = document.querySelector(".dropdown");

	if (
		dropdownVisible.value &&
		inputElement &&
		dropdownElement &&
		!inputElement.contains(target) &&
		!dropdownElement.contains(target)
	) {
		dropdownVisible.value = false;
	}
};

const handleEscapeKey = (event: KeyboardEvent) => {
	if (event.key === "Escape" && dropdownVisible.value) {
		dropdownVisible.value = false;
	}
};

onMounted(() => {
	document.addEventListener("mousedown", handleClickOutside);
	document.addEventListener("keydown", handleEscapeKey);
});

onBeforeUnmount(() => {
	document.removeEventListener("mousedown", handleClickOutside);
	document.removeEventListener("keydown", handleEscapeKey);
});
</script>

<template>
  <div class="relative">
    <input 
      id="school" 
      type="text" 
      v-model="inputValue" 
      @focus="handleChange()" 
      @click="handleChange()"
      @blur="handleBlur"
      :placeholder="t('form.search_school')"
      class="field-input" 
      :class="inputValue && dropdownVisible ? 'rounded-t-md' : 'rounded-md'" 
      autocomplete="off" 
    />
    <div v-if="inputValue && dropdownVisible" class="dropdown">
      <div 
        v-for="schoolOption in filteredSchools" 
        :key="schoolOption.id?.toString()" 
        @mousedown="selectSchool(schoolOption)"
        class="dropdown-result"
      >
        {{ schoolOption.attributes?.name }}
      </div>
      <div v-if="!filteredSchools.length && dropdownVisible" class="p-2">
        {{ t('form.no_results') }}
      </div>
    </div>
    <p v-if="error" class="mt-2 text-sm text-red-600">{{ error }}</p>
  </div>
</template>

<style scoped>
.field-input {
  @apply mt-1 block w-full p-2 border border-gray-300 text-black focus:outline-none focus:ring-0;
}

.dropdown {
  @apply border-2 border-gray-300 rounded-b max-h-48 overflow-y-auto shadow-md bg-white absolute w-full z-10;
}

.dropdown div {
  @apply p-2 border-b-2 border-gray-200 last:border-b-0 text-black;

  &.dropdown-result {
    @apply hover:bg-gray-100 cursor-pointer;
  }
}
</style>