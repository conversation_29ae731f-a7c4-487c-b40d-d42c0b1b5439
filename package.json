{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --dotenv .env.local", "public": "nuxt dev --host 0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "types": "strapi-generate-types quickgen $FISHBUSH_API_URL", "format": "biome check --no-errors-on-unmatched --write .", "env": "sync-dotenv --env .env"}, "dependencies": {"@fortawesome/vue-fontawesome": "^3.0.8", "@nuxt/content": "^2.13.1", "@nuxt/types": "^2.17.4", "@nuxtjs/google-gtag": "^1.0.4", "@nuxtjs/i18n": "^8.5.1", "@nuxtjs/robots": "^4.1.8", "@nuxtjs/sitemap": "^6.1.1", "@nuxtjs/tailwindcss": "^6.12.0", "@pinia/nuxt": "^0.5.1", "@tailwindcss/forms": "^0.5.9", "@tour-de-app/authfish": "1.2.0", "cheerio": "^1.0.0", "fast-fuzzy": "^1.12.0", "hono": "^4.6.4", "nuxt": "^3.12.2", "nuxt-gtag": "^3.0.1", "nuxt-schema-org": "^3.4.1", "nuxt-zod-i18n": "^1.9.0", "pinia": "^2.1.7", "qs": "^6.13.0", "string-ts": "^2.2.0", "uuid": "^10.0.0", "vue": "^3.4.29", "vue-router": "^4.3.3", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@tailwindcss/typography": "^0.5.13", "@types/uuid": "^10.0.0", "husky": "^9.1.6", "strapi-generate-types": "^1.0.0", "tailwind-scrollbar": "^3.1.0", "typescript": "^5.5.2", "vue-tsc": "^1"}}